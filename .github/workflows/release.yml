name: Release

on:
  push:
    tags:
      - v**

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  release:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GitHub_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install deps
        run: npm install

      - name: Build
        run: |
          npm run build

      - name: Release to GitHub
        run: |
          npm run release
          sleep 1s

      - name: Notify release
        uses: apexskier/github-release-commenter@v1
        continue-on-error: true
        with:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          comment-template: |
            :rocket: _This ticket has been resolved in {release_tag}. See {release_link} for release notes._
