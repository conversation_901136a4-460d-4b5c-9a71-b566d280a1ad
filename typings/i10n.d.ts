// Generated by zotero-plugin-scaffold
/* prettier-ignore */
/* eslint-disable */
// @ts-nocheck
export type FluentMessageId =
  | 'item-info-row-example-label'
  | 'item-section-example1-head-text'
  | 'item-section-example1-sidenav-tooltip'
  | 'item-section-example2-button-tooltip'
  | 'item-section-example2-head-text'
  | 'item-section-example2-sidenav-tooltip'
  | 'menuitem-filemenulabel'
  | 'menuitem-label'
  | 'menuitem-submenulabel'
  | 'menupopup-label'
  | 'paper-assistant-button-label'
  | 'paper-assistant-button-tooltip'
  | 'paper-assistant-chat-placeholder'
  | 'paper-assistant-quick-actions'
  | 'paper-assistant-research-method'
  | 'paper-assistant-research-results'
  | 'paper-assistant-send-full-doc'
  | 'paper-assistant-smart-note'
  | 'paper-assistant-summarize'
  | 'paper-assistant-title'
  | 'pref-dev-logLLM'
  | 'pref-enable'
  | 'pref-help'
  | 'pref-input'
  | 'pref-llm-apiKey'
  | 'pref-llm-baseURL'
  | 'pref-llm-model'
  | 'pref-llm-provider'
  | 'pref-llm-timeoutMs'
  | 'pref-llm-title'
  | 'pref-title'
  | 'prefs-table-detail'
  | 'prefs-table-title'
  | 'prefs-title'
  | 'startup-begin'
  | 'startup-finish'
  | 'tabpanel-lib-tab-label'
  | 'tabpanel-reader-tab-label';
