{"name": "zotero-ai-plugin", "type": "module", "version": "1.0.0", "description": "Zotero AI Plugin - AI assistant for reading and organizing research papers", "config": {"addonName": "Zotero AI Assistant", "addonID": "<EMAIL>", "addonRef": "zotero-ai-assistant", "addonInstance": "ZoteroAIAssistant", "prefsPrefix": "extensions.zotero.ai-assistant"}, "repository": {"type": "git", "url": "git+https://github.com/hua3467/zotero-ai-plugin.git"}, "author": "aayang", "bugs": {"url": "https://github.com/hua3467/zotero-ai-plugin/issues"}, "homepage": "https://github.com/hua3467/zotero-ai-plugin#readme", "license": "AGPL-3.0-or-later", "scripts": {"start": "zotero-plugin serve", "build": "zotero-plugin build && tsc --noEmit", "lint:check": "prettier --check . && eslint .", "lint:fix": "prettier --write . && eslint . --fix", "release": "zotero-plugin release", "test": "zotero-plugin test", "update-deps": "npm update --save"}, "dependencies": {"zotero-plugin-toolkit": "^5.1.0-beta.4"}, "devDependencies": {"@types/chai": "^5.2.2", "@types/mocha": "^10.0.10", "@types/node": "^24.2.0", "@zotero-plugin/eslint-config": "^0.6.7", "chai": "^5.2.1", "eslint": "^9.32.0", "mocha": "^11.7.1", "prettier": "^3.6.2", "typescript": "^5.9.2", "zotero-plugin-scaffold": "^0.8.0", "zotero-types": "^4.1.0-beta.1"}, "prettier": {"printWidth": 80, "tabWidth": 2, "endOfLine": "lf", "overrides": [{"files": ["*.xhtml"], "options": {"htmlWhitespaceSensitivity": "css"}}]}}