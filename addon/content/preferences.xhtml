<linkset>
  <html:link rel="localization" href="__addonRef__-preferences.ftl" />
</linkset>
<groupbox onload="Zotero.__addonInstance__.hooks.onPrefsEvent('load', { window });">
  <label>
    <html:h2 data-l10n-id="pref-title"></html:h2>
  </label>
  <checkbox id="zotero-prefpane-__addonRef__-enable" preference="enable" data-l10n-id="pref-enable" />
  <hbox>
    <html:label for="zotero-prefpane-__addonRef__-input" data-l10n-id="pref-input"></html:label>
    <html:input type="text" id="zotero-prefpane-__addonRef__-input" preference="input"></html:input>
  </hbox>

  <!-- LLM/API settings -->
  <label>
    <html:h2 data-l10n-id="pref-llm-title"></html:h2>
  </label>
  <hbox>
    <html:label for="zpa-llm-provider" data-l10n-id="pref-llm-provider"></html:label>
    <html:select id="zpa-llm-provider" preference="llm.provider">
      <html:option value="openrouter">OpenRouter</html:option>
      <html:option value="siliconflow">SiliconFlow</html:option>
      <html:option value="deepseek">DeepSeek</html:option>
      <html:option value="gemini">Gemini</html:option>
      <html:option value="claude">Claude</html:option>
      <html:option value="openai">OpenAI</html:option>
    </html:select>
  </hbox>
  <hbox>
    <html:label for="zpa-llm-apiKey" data-l10n-id="pref-llm-apiKey"></html:label>
    <html:input type="password" id="zpa-llm-apiKey" preference="llm.apiKey" />
  </hbox>
  <hbox>
    <html:label for="zpa-llm-model" data-l10n-id="pref-llm-model"></html:label>
    <html:input type="text" id="zpa-llm-model" preference="llm.model" />
  </hbox>
  <hbox>
    <html:label for="zpa-llm-baseURL" data-l10n-id="pref-llm-baseURL"></html:label>
    <html:input type="text" id="zpa-llm-baseURL" preference="llm.baseURL" />
  </hbox>
  <hbox>
    <html:label for="zpa-llm-timeout" data-l10n-id="pref-llm-timeoutMs"></html:label>
    <html:input type="number" id="zpa-llm-timeout" preference="llm.timeoutMs" min="1000" step="1000" />
  </hbox>
  <checkbox id="zpa-dev-logLLM" preference="dev.logLLM" data-l10n-id="pref-dev-logLLM" />
  <hbox class="virtualized-table-container" flex="1" height="300px">
    <html:div id="__addonRef__-table-container" />
  </hbox>
</groupbox>
<vbox>
  <html:label data-l10n-id="pref-help"
    data-l10n-args='{"time": "__buildTime__","name": "__addonName__","version":"__buildVersion__"}'></html:label>
</vbox>