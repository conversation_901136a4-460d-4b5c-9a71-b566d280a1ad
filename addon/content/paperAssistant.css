/* Paper Assistant UI Styles */

/* Floating Window Container */
#ai-assistant-floating-container {
  width: 100% !important;
  height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  background-color: white !important;
  font-family:
    system-ui,
    -apple-system,
    sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Toolbar <PERSON><PERSON> Styles */
#ai-assistant-toolbar-button {
  margin-left: 4px !important;
}

#ai-assistant-toolbar-button .toolbarbutton-icon {
  width: 16px !important;
  height: 16px !important;
}

/* Legacy panel styles (kept for compatibility) */
#paper-assistant-panel {
  position: fixed !important;
  top: 60px !important;
  right: 20px !important;
  width: 400px !important;
  height: 600px !important;
  background-color: white !important;
  border: 1px solid #ccc !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1000 !important;
  display: flex !important;
  flex-direction: column !important;
  font-family:
    system-ui,
    -apple-system,
    sans-serif !important;
}

/* Header styles for both floating window and legacy panel */
#ai-assistant-floating-container .assistant-header,
#paper-assistant-panel .assistant-header {
  padding: 16px !important;
  border-bottom: 1px solid #eee !important;
  font-weight: bold !important;
  font-size: 16px !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background-color: #f8f9fa !important;
}

#ai-assistant-floating-container .assistant-close-btn,
#paper-assistant-panel .assistant-close-btn {
  background: none !important;
  border: none !important;
  font-size: 20px !important;
  cursor: pointer !important;
  padding: 0 !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  transition: background-color 0.2s !important;
}

#ai-assistant-floating-container .assistant-close-btn:hover,
#paper-assistant-panel .assistant-close-btn:hover {
  background-color: #e9ecef !important;
}

/* Quick actions styles */
#ai-assistant-floating-container .quick-actions-section,
#paper-assistant-panel .quick-actions-section {
  padding: 16px !important;
  border-bottom: 1px solid #eee !important;
}

/* Controls bar (top) */
#ai-assistant-floating-container .assistant-controls-bar,
#paper-assistant-panel .assistant-controls-bar {
  padding: 12px 16px !important;
  border-bottom: 1px solid #eee !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  background: #fff !important;
}

#ai-assistant-floating-container .quick-actions-title,
#paper-assistant-panel .quick-actions-title {
  font-weight: bold !important;
  margin-bottom: 12px !important;
  font-size: 14px !important;
}

#ai-assistant-floating-container .quick-actions-grid,
#paper-assistant-panel .quick-actions-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  gap: 8px !important;
}

#ai-assistant-floating-container .quick-action-btn,
#paper-assistant-panel .quick-action-btn {
  padding: 8px 12px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  background-color: #f8f9fa !important;
  cursor: pointer !important;
  font-size: 12px !important;
  transition: background-color 0.2s !important;
}

#ai-assistant-floating-container .quick-action-btn:hover,
#paper-assistant-panel .quick-action-btn:hover {
  background-color: #e9ecef !important;
}

/* Chat section styles */
#ai-assistant-floating-container .chat-section,
#paper-assistant-panel .chat-section {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  padding: 16px !important;
  overflow: hidden !important; /* keep inner scroll only in messages */
}

#ai-assistant-floating-container #chat-messages,
#paper-assistant-panel #chat-messages {
  flex: 1 !important;
  overflow-y: auto !important;
  margin-bottom: 12px !important;
  padding: 8px !important;
  border: 1px solid #eee !important;
  border-radius: 4px !important;
  background-color: #fafafa !important;
  min-height: 200px !important;
}

#ai-assistant-floating-container .chat-input-container,
#paper-assistant-panel .chat-input-container {
  display: flex !important;
  gap: 8px !important;
  position: sticky !important;
  bottom: 0 !important;
  background: white !important;
  padding-top: 8px !important;
  border-top: 1px solid #eee !important;
}

#ai-assistant-floating-container #chat-input,
#paper-assistant-panel #chat-input {
  flex: 1 !important;
  padding: 8px !important;
  border: 1px solid #ddd !important;
  border-radius: 4px !important;
  resize: none !important;
  font-size: 14px !important;
  font-family: inherit !important;
}

#ai-assistant-floating-container .chat-send-btn,
#paper-assistant-panel .chat-send-btn {
  padding: 8px 16px !important;
  background-color: #007acc !important;
  color: white !important;
  border: none !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-size: 14px !important;
  transition: background-color 0.2s !important;
}

#ai-assistant-floating-container .chat-send-btn:hover,
#paper-assistant-panel .chat-send-btn:hover {
  background-color: #0056b3 !important;
}

#ai-assistant-floating-container .chat-message,
#paper-assistant-panel .chat-message {
  margin-bottom: 12px !important;
  padding: 8px 12px !important;
  border-radius: 8px !important;
  max-width: 85% !important;
  word-wrap: break-word !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
}

#ai-assistant-floating-container .chat-message.user,
#paper-assistant-panel .chat-message.user {
  background-color: #007acc !important;
  color: white !important;
  margin-left: auto !important;
  text-align: right !important;
}

#ai-assistant-floating-container .chat-message.assistant,
#paper-assistant-panel .chat-message.assistant {
  background-color: #e9ecef !important;
  color: #333 !important;
  margin-right: auto !important;
}

/* Minimal markdown styling */
#ai-assistant-floating-container .md-container h3,
#paper-assistant-panel .md-container h3 {
  margin: 6px 0 4px 0 !important;
  font-size: 14px !important;
}
#ai-assistant-floating-container .md-container h4,
#paper-assistant-panel .md-container h4 {
  margin: 6px 0 4px 0 !important;
  font-size: 13px !important;
}
#ai-assistant-floating-container .md-container ul,
#paper-assistant-panel .md-container ul,
#ai-assistant-floating-container .md-container ol,
#paper-assistant-panel .md-container ol {
  margin: 6px 0 6px 18px !important;
  padding: 0 !important;
}
#ai-assistant-floating-container .md-container code,
#paper-assistant-panel .md-container code {
  background: #f1f3f5 !important;
  padding: 1px 4px !important;
  border-radius: 4px !important;
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace !important;
  font-size: 12px !important;
}
#ai-assistant-floating-container .md-container pre,
#paper-assistant-panel .md-container pre {
  background: #f1f3f5 !important;
  padding: 8px !important;
  border-radius: 6px !important;
  overflow: auto !important;
}
#ai-assistant-floating-container .md-container a,
#paper-assistant-panel .md-container a {
  color: #0056b3 !important;
  text-decoration: none !important;
}
#ai-assistant-floating-container .md-container a:hover,
#paper-assistant-panel .md-container a:hover {
  text-decoration: underline !important;
}
