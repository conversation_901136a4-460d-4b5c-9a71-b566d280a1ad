import { getString } from "../utils/locale";
import { callLLM, ChatMessage } from "./llm";

export class PaperAssistantFactory {
  private static toolbarButtonInitialized = false;
  private static floatingWindow: Window | null = null;
  private static sendFullDoc: boolean = false;
  private static activeReaderTabId: string | undefined;
  private static lastContextDesc: string | undefined;
  private static periodicCheckInterval: number | undefined;

  /**
   * Reset initialization state - used for plugin reload
   */
  static reset() {
    this.toolbarButtonInitialized = false;
    if (this.floatingWindow && !this.floatingWindow.closed) {
      this.floatingWindow.close();
    }
    this.floatingWindow = null;
  }
  
  /**
   * Register stylesheet for Paper Assistant
   */
  static registerStyleSheet(win: Window) {
    const doc = win.document;
    const styles = ztoolkit.UI.createElement(doc, "link");
    styles.setAttribute("type", "text/css");
    styles.setAttribute("rel", "stylesheet");
    styles.setAttribute("href", `chrome://${addon.data.config.addonRef}/content/paperAssistant.css`);
    doc.documentElement?.appendChild(styles);
  }

  /**
   * Register the AI Assistant menu items and toolbar button
   */
  static registerToolbarButton() {
    if (this.toolbarButtonInitialized) {
      return;
    }

    const menuIcon = `chrome://${addon.data.config.addonRef}/content/icons/<EMAIL>`;
    
    // Register in Tools menu as primary access point
    ztoolkit.Menu.register("menuTools", {
      tag: "menuitem",
      id: "ai-assistant-tools-menuitem",
      label: getString("paper-assistant-button-label"),
      commandListener: () => addon.hooks.onDialogEvents("showPaperAssistant"),
      icon: menuIcon,
    });

    // Also register in item context menu for convenience
    ztoolkit.Menu.register("item", {
      tag: "menuitem",
      id: "ai-assistant-item-menuitem", 
      label: getString("paper-assistant-button-label"),
      commandListener: () => addon.hooks.onDialogEvents("showPaperAssistant"),
      icon: menuIcon,
    });

    this.toolbarButtonInitialized = true;
  }

  /**
   * Show the Paper Assistant UI as a floating window
   */
  static showPaperAssistant() {
    ztoolkit.log("showPaperAssistant called");
    
    // If window already exists and is open, focus it and update content
    if (this.floatingWindow && !this.floatingWindow.closed) {
      this.floatingWindow.focus();
      // Update the welcome message to reflect current context
      this.updateWelcomeMessage();
      return;
    }
    
    // Create new floating window
    this.createFloatingWindow();
  }

  /**
   * Create a floating window for the AI Assistant
   */
  private static createFloatingWindow() {
    const mainWindow = Zotero.getMainWindow();
    if (!mainWindow) {
      ztoolkit.log("Could not get main window for floating window creation");
      return;
    }

    // Calculate position for right edge of screen
    const screenWidth = mainWindow.screen.width;
    const windowWidth = 400;
    const windowHeight = 600;
    const rightPosition = screenWidth - windowWidth - 20; // 20px margin from edge

    // Window features for the floating window
    const features = [
      "chrome=yes",
      "titlebar=yes",
      "resizable=yes",
      "scrollbars=no",
      "status=no",
      "modal=no",
      `width=${windowWidth}`,
      `height=${windowHeight}`,
      `left=${rightPosition}`,
      "top=100"
    ].join(",");

    // Create a window using a blank HTML document instead of commonDialog
    this.floatingWindow = mainWindow.open(
      "about:blank",
      "ai-assistant-floating",
      features
    ) as Window;

    if (!this.floatingWindow) {
      ztoolkit.log("Failed to create floating window");
      return;
    }

    // Wait for window to load, then customize it
    this.floatingWindow.addEventListener("load", () => {
      this.customizeFloatingWindow();
    });
  }

  /**
   * Customize the floating window with AI Assistant content
   */
  private static customizeFloatingWindow() {
    if (!this.floatingWindow) return;

    const doc = this.floatingWindow.document;
    
    // Set window title
    doc.title = getString("paper-assistant-title");
    
    // Add stylesheet
    const linkElement = doc.createElement("link");
    linkElement.rel = "stylesheet";
    linkElement.href = `chrome://${addon.data.config.addonRef}/content/paperAssistant.css`;
    if (doc.head) {
      doc.head.appendChild(linkElement);
    }
    
    // Add basic styles
    const styleElement = doc.createElement("style");
    styleElement.textContent = `
      body {
        margin: 0;
        padding: 0;
        font-family: system-ui, -apple-system, sans-serif;
        height: 100vh;
        overflow: hidden;
        background: white;
      }
    `;
    if (doc.head) {
      doc.head.appendChild(styleElement);
    }

    // Clear body and add our content
    if (doc.body) {
      doc.body.innerHTML = "";
      
      // Create custom content
      const assistantContent = this.createFloatingWindowContent(doc);
      doc.body.appendChild(assistantContent);
      
      // Show welcome message
      setTimeout(() => {
        const messagesArea = doc.getElementById("chat-messages") as HTMLElement;
        if (messagesArea) {
          this.addChatMessage(
            messagesArea,
            this.getContextualWelcomeMessage(),
            "assistant"
          );
        }
      }, 100);
    }
  }

  /**
   * Update the welcome message in the existing window
   */
  private static updateWelcomeMessage() {
    if (!this.floatingWindow || this.floatingWindow.closed) {
      return;
    }

    const doc = this.floatingWindow.document;
    const messagesArea = doc.getElementById("chat-messages") as HTMLElement;
    if (messagesArea) {
      // Check if there are existing messages (more than just the welcome message)
      const existingMessages = messagesArea.querySelectorAll('.chat-message');
      
      const newDesc = this.getCurrentContextDescription();
      if (newDesc === this.lastContextDesc) {
        return;
      }
      this.lastContextDesc = newDesc;

      if (existingMessages.length <= 1) {
        // Only welcome message exists, replace it
        messagesArea.innerHTML = "";
        this.addChatMessage(
          messagesArea,
          this.getContextualWelcomeMessage(),
          "assistant"
        );
      } else {
        // There's a conversation going, add a context update message
        this.addChatMessage(
          messagesArea,
          `📄 Context updated: ${newDesc}`,
          "assistant"
        );
      }
    }
  }

  /**
   * Get a brief description of current context for updates
   */
  private static getCurrentContextDescription(): string {
    // Check if there's an active reader
    const reader = this.getActiveReader();
    if (reader && reader._window) {
      const item = reader.itemID ? Zotero.Items.get(reader.itemID) : null;
      if (item) {
        let title = item.getField('title') || 'this document';
        if (item.isAttachment()) {
          const parentItem = item.parentItem;
          if (parentItem) {
            title = parentItem.getField('title') || 'this document';
          }
        }
        return `Now viewing "${title}"`;
      }
      return "Now viewing a PDF document";
    }
    
    // Check if there's a selected item
    const selectedItems = Zotero.getActiveZoteroPane()?.getSelectedItems();
    if (selectedItems && selectedItems.length > 0) {
      if (selectedItems.length === 1) {
        const title = selectedItems[0].getField('title') || 'this item';
        return `Now selected "${title}"`;
      } else {
        return `Now selected ${selectedItems.length} items`;
      }
    }
    
    return "No specific item selected";
  }

  /**
   * Get a contextual welcome message based on current state
   */
  private static getContextualWelcomeMessage(): string {
    // Check if there's an active reader
    const reader = this.getActiveReader();
    if (reader && reader._window) {
      // Try to get the item from the reader
      const item = reader.itemID ? Zotero.Items.get(reader.itemID) : null;
      if (item) {
        let title = item.getField('title') || 'this document';
        
        // If this is a PDF attachment, get the parent item's title
        if (item.isAttachment()) {
          const parentItem = item.parentItem;
          if (parentItem) {
            title = parentItem.getField('title') || 'this document';
          }
        }
        
        return `Welcome to AI Assistant! I can help you analyze "${title}". Try asking me to summarize it or use the quick actions below.`;
      }
      return "Welcome to AI Assistant! I can help you analyze the currently open PDF. Try asking me to summarize it or use the quick actions below.";
    }
    
    // Check if there's a selected item
    const selectedItems = Zotero.getActiveZoteroPane()?.getSelectedItems();
    if (selectedItems && selectedItems.length > 0) {
      if (selectedItems.length === 1) {
        const item = selectedItems[0];
        const title = item.getField('title') || 'this item';
        return `Welcome to AI Assistant! I can see you have "${title}" selected. I can help you analyze this item or answer questions about your research.`;
      } else {
        // Multiple items selected - show first few titles
        const titles = selectedItems.slice(0, 2).map(item => 
          item.getField('title') || 'Untitled'
        );
        const displayText = titles.join('", "');
        const moreText = selectedItems.length > 2 ? ` and ${selectedItems.length - 2} more` : '';
        return `Welcome to AI Assistant! I can see you have ${selectedItems.length} items selected including "${displayText}"${moreText}. I can help you analyze these items or answer questions about your research.`;
      }
    }
    
    return "Welcome to AI Assistant! Open a PDF or select items in your library to get started, or ask me general questions about your research.";
  }

  /**
   * Create content for the floating window
   */
  private static createFloatingWindowContent(doc: Document): HTMLElement {
    const container = doc.createElement("div");
    container.id = "ai-assistant-floating-container";
    container.style.cssText = `
      width: 100%;
      height: 100vh;
      display: flex;
      flex-direction: column;
      background: white;
    `;

    // Create controls bar (e.g., Send Full Document)
    const controlsBar = this.createControlsBar(doc);

    // Create quick actions section
    const quickActionsSection = this.createQuickActionsSection(doc);
    
    // Create chat section
    const chatSection = this.createChatSection(doc);
    
    // Assemble container (no header)
    container.appendChild(controlsBar);
    container.appendChild(quickActionsSection);
    container.appendChild(chatSection);

    return container;
  }



  /**
   * Controls bar with toggles
   */
  private static createControlsBar(doc: Document): HTMLElement {
    const bar = doc.createElement("div");
    bar.className = "assistant-controls-bar";

    const fullDocLabel = doc.createElement("label");
    fullDocLabel.style.display = "inline-flex";
    fullDocLabel.style.alignItems = "center";
    fullDocLabel.style.gap = "6px";

    const checkbox = doc.createElement("input");
    checkbox.type = "checkbox";
    checkbox.id = "ai-send-full-doc";
    checkbox.checked = PaperAssistantFactory.sendFullDoc;
    checkbox.addEventListener("change", () => {
      PaperAssistantFactory.sendFullDoc = checkbox.checked;
    });

    const text = doc.createElement("span");
    text.textContent = getString("paper-assistant-send-full-doc");

    fullDocLabel.appendChild(checkbox);
    fullDocLabel.appendChild(text);
    bar.appendChild(fullDocLabel);
    return bar;
  }

  /**
   * Create the quick actions section
   */
  private static createQuickActionsSection(doc: Document): HTMLElement {
    const section = doc.createElement("div");
    section.className = "quick-actions-section";

    const title = doc.createElement("div");
    title.textContent = getString("paper-assistant-quick-actions");
    title.className = "quick-actions-title";

    const actionsContainer = doc.createElement("div");
    actionsContainer.className = "quick-actions-grid";

    // Quick action buttons
    const actions = [
      { key: "summarize", label: getString("paper-assistant-summarize") },
      { key: "research-method", label: getString("paper-assistant-research-method") },
      { key: "research-results", label: getString("paper-assistant-research-results") },
      { key: "smart-note", label: getString("paper-assistant-smart-note") },
    ];

    actions.forEach((action) => {
      const button = doc.createElement("button");
      button.textContent = action.label;
      button.className = "quick-action-btn";
      
      button.addEventListener("click", () => this.handleQuickAction(action.key));
      
      actionsContainer.appendChild(button);
    });

    section.appendChild(title);
    section.appendChild(actionsContainer);
    return section;
  }

  /**
   * Create the chat section
   */
  private static createChatSection(doc: Document): HTMLElement {
    const section = doc.createElement("div");
    section.className = "chat-section";

    // Chat messages area
    const messagesArea = doc.createElement("div");
    messagesArea.id = "chat-messages";

    // Input area
    const inputContainer = doc.createElement("div");
    inputContainer.className = "chat-input-container";

    const chatInput = doc.createElement("textarea");
    chatInput.id = "chat-input";
    chatInput.placeholder = getString("paper-assistant-chat-placeholder");
    chatInput.rows = 2;

    const sendButton = doc.createElement("button");
    sendButton.textContent = "Send";
    sendButton.className = "chat-send-btn";

    // Event listeners
    sendButton.addEventListener("click", () => 
      this.handleChatMessage(chatInput, messagesArea)
    );
    
    chatInput.addEventListener("keydown", (e: KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        this.handleChatMessage(chatInput, messagesArea);
      }
    });

    inputContainer.appendChild(chatInput);
    inputContainer.appendChild(sendButton);

    section.appendChild(messagesArea);
    section.appendChild(inputContainer);
    return section;
  }

  /**
   * Handle quick action button clicks
   */
  private static async handleQuickAction(actionKey: string) {
    ztoolkit.log(`Quick action triggered: ${actionKey}`);
    
    if (!this.floatingWindow || this.floatingWindow.closed) {
      ztoolkit.log("No floating window found");
      return;
    }

    // Add message to the chat in floating window
    const messagesArea = this.floatingWindow.document.getElementById("chat-messages") as HTMLElement;
    if (messagesArea) {
      const thinking = this.addThinkingMessage(messagesArea, `Processing ${actionKey}…`);
      const context = await this.buildContextTextAsync();
      const prompt = this.buildQuickActionPrompt(actionKey, context);
      const messages: ChatMessage[] = [
        { role: "system", content: "You are an academic research assistant for Zotero users. Be concise and accurate." },
        { role: "user", content: prompt },
      ];
      this.callModelAndRender(messages, messagesArea, thinking);
    }
  }

  /**
   * Handle chat message sending
   */
  private static async handleChatMessage(input: HTMLTextAreaElement, messagesArea: HTMLElement) {
    const message = input.value.trim();
    if (!message) return;

    // Add user message
    this.addChatMessage(messagesArea, message, "user");
    
    // Clear input
    input.value = "";
    const thinking = this.addThinkingMessage(messagesArea, "Thinking…");
    const context = await this.buildContextTextAsync();
    const userWithContext = `Context about the current item (may be partial):\n${context}\n\nQuestion: ${message}`;
    const messages: ChatMessage[] = [
      { role: "system", content: "You are an academic research assistant for Zotero users. Use provided context, and say when information is unavailable." },
      { role: "user", content: userWithContext },
    ];
    this.callModelAndRender(messages, messagesArea, thinking);
  }

  /**
   * Add a message to the chat area
   */
  private static addChatMessage(messagesArea: HTMLElement, message: string, sender: "user" | "assistant") {
    const doc = messagesArea.ownerDocument;
    if (!doc) return;
    
    const messageDiv = doc.createElement("div");
    messageDiv.className = `chat-message ${sender}`;
    if (sender === "assistant") {
      const rendered = this.renderMarkdown(doc, message);
      messageDiv.appendChild(rendered);
    } else {
      messageDiv.textContent = message;
    }
    messagesArea.appendChild(messageDiv);

    // Scroll to bottom
    messagesArea.scrollTop = messagesArea.scrollHeight;
  }

  /**
   * EDIT 2025-08-08: LLM integration helpers - AA
   */
  private static addThinkingMessage(messagesArea: HTMLElement, text: string) {
    const doc = messagesArea.ownerDocument!;
    const thinkingDiv = doc.createElement("div");
    thinkingDiv.className = "chat-message assistant";
    thinkingDiv.textContent = text;
    messagesArea.appendChild(thinkingDiv);
    messagesArea.scrollTop = messagesArea.scrollHeight;
    return thinkingDiv;
  }

  private static async callModelAndRender(messages: ChatMessage[], messagesArea: HTMLElement, thinkingNode: HTMLElement) {
    try {
      const reply = await callLLM({ messages });
      thinkingNode.remove();
      this.addChatMessage(messagesArea, reply, "assistant");
    } catch (err: any) {
      thinkingNode.remove();
      const errorText = (err && err.message) ? err.message : String(err);
      this.addChatMessage(messagesArea, `Error: ${errorText}`, "assistant");
    }
  }

  private static buildContextText(): string {
    // Try active reader first
    const reader = this.getActiveReader();
    let item: Zotero.Item | null = null;
    if (reader && reader._window) {
      item = reader.itemID ? Zotero.Items.get(reader.itemID) : null;
    }
    if (!item) {
      const selected = Zotero.getActiveZoteroPane()?.getSelectedItems();
      if (selected && selected.length > 0) {
        item = selected[0];
      }
    }
    if (!item) {
      return "No item selected or open.";
    }
    // If attachment, use parent item for metadata
    const metaItem = item.isAttachment() ? item.parentItem || item : item;
    const title = metaItem.getField("title") || "Untitled";
    const abstractNote = metaItem.getField("abstractNote") || "";
    return `Title: ${title}\nAbstract: ${abstractNote || "(none)"}`;
  }

  /**
   * Try to find the currently active reader instead of assuming index 0
   */
  private static getActiveReader(): any | null {
    const Reader: any = (Zotero as any).Reader;
    if (!Reader || !Array.isArray(Reader._readers) || Reader._readers.length === 0) {
      ztoolkit.log("PaperAssistant: No readers available");
      return null;
    }

    ztoolkit.log(`PaperAssistant: Found ${Reader._readers.length} readers`);

    // Log all available readers for debugging
    Reader._readers.forEach((r: any, index: number) => {
      const tabId = r?._tab?.id || r?._tab?.getAttribute?.("id");
      const itemId = r?.itemID;
      const item = itemId ? Zotero.Items.get(itemId) : null;
      const title = item ? (item.isAttachment() ? item.parentItem?.getField('title') : item.getField('title')) : 'Unknown';
      ztoolkit.log(`PaperAssistant: Reader ${index}: tabId=${tabId}, itemId=${itemId}, title="${title}"`);
    });

    // Try to resolve currently selected tab id from DOM/Zotero_Tabs
    try {
      const zTabs = (ztoolkit.getGlobal("Zotero_Tabs") as any) || undefined;
      const domSelectedId = String(
        zTabs?._tabbox?.selectedTab?.id ??
        zTabs?.selectedID ??
        zTabs?._selectedID ??
        zTabs?.selected?.id ??
        zTabs?.selected?.getAttribute?.("id") ?? ""
      );
      if (domSelectedId && domSelectedId !== "") {
        ztoolkit.log(`PaperAssistant: DOM selected tab ID: ${domSelectedId}`);
        this.activeReaderTabId = domSelectedId;
      }
    } catch (e) {
      ztoolkit.log("PaperAssistant: Error getting DOM selected tab ID:", e);
    }

    ztoolkit.log(`PaperAssistant: Current activeReaderTabId: ${this.activeReaderTabId}`);

    // 0) If we have an explicit active tab id, use it
    if (this.activeReaderTabId) {
      const byId = Reader._readers.find((r: any) => {
        const tabId = String(r?._tab?.id || r?._tab?.getAttribute?.("id") || "");
        return tabId === String(this.activeReaderTabId);
      });
      if (byId) {
        ztoolkit.log(`PaperAssistant: Found reader by tab ID: ${this.activeReaderTabId}`);
        return byId;
      } else {
        ztoolkit.log(`PaperAssistant: No reader found for tab ID: ${this.activeReaderTabId}`);
      }
    }

    // 1) Prefer the reader whose iframe document has focus
    const focused = Reader._readers.find((r: any) => r?._iframeWindow?.document?.hasFocus?.());
    if (focused) {
      ztoolkit.log("PaperAssistant: Found focused reader");
      return focused;
    }

    // 2) Prefer a reader whose tab is selected in the current main window
    try {
      const win = Zotero.getMainWindow();
      const selected = Reader._readers.find((r: any) => {
        const t: any = r?._tab;
        const isSelected = t?.selected === true ||
                          t?.tab?.getAttribute?.("selected") === "true" ||
                          t?.getAttribute?.("selected") === "true";
        return isSelected;
      });
      if (selected) {
        ztoolkit.log("PaperAssistant: Found selected reader by tab selection");
        return selected;
      }
    } catch (e) {
      ztoolkit.log("PaperAssistant: Error finding selected reader:", e);
    }

    // 3) Fallback to the most recently added reader
    const fallback = Reader._readers[Reader._readers.length - 1] || Reader._readers[0] || null;
    if (fallback) {
      ztoolkit.log("PaperAssistant: Using fallback reader (most recent)");
    } else {
      ztoolkit.log("PaperAssistant: No fallback reader available");
    }
    return fallback;
  }

  /** Expose context change hook for external notifications */
  static contextChanged() {
    ztoolkit.log("PaperAssistant: Context changed, updating welcome message");
    this.updateWelcomeMessage();
  }

  /** Force refresh the active reader detection */
  static refreshActiveReader() {
    ztoolkit.log("PaperAssistant: Force refreshing active reader");
    this.activeReaderTabId = undefined; // Clear cached tab ID
    const reader = this.getActiveReader(); // This will re-detect the active reader
    if (reader) {
      const item = reader.itemID ? Zotero.Items.get(reader.itemID) : null;
      const title = item ? (item.isAttachment() ? item.parentItem?.getField('title') : item.getField('title')) : 'Unknown';
      ztoolkit.log(`PaperAssistant: Refreshed to reader with title: "${title}"`);
    }
    this.updateWelcomeMessage();
  }

  /** External hook to set active reader by tab id */
  static setActiveReaderByTabId(tabId: string) {
    const newTabId = String(tabId);
    ztoolkit.log(`PaperAssistant: Setting active reader tab ID to: ${newTabId}`);

    // Only update if the tab ID actually changed
    if (this.activeReaderTabId !== newTabId) {
      this.activeReaderTabId = newTabId;
      ztoolkit.log(`PaperAssistant: Tab ID changed, updating welcome message`);
      this.updateWelcomeMessage();
    } else {
      ztoolkit.log(`PaperAssistant: Tab ID unchanged, skipping update`);
    }
  }

  private static async buildContextTextAsync(): Promise<string> {
    if (!PaperAssistantFactory.sendFullDoc) {
      return this.buildContextText();
    }
    try {
      // Resolve current item
      const reader = this.getActiveReader();
      let item: Zotero.Item | null = null;
      if (reader && reader._window) {
        item = reader.itemID ? Zotero.Items.get(reader.itemID) : null;
      }
      if (!item) {
        const selected = Zotero.getActiveZoteroPane()?.getSelectedItems();
        if (selected && selected.length > 0) {
          item = selected[0];
        }
      }
      if (!item) return this.buildContextText();

      // Gather metadata
      const metaItem = item.isAttachment() ? item.parentItem || item : item;
      const title = metaItem.getField("title") || "Untitled";
      const abstractNote = metaItem.getField("abstractNote") || "";

      // Gather text from PDFs
      let fullText = "";
      const pushText = (t: any) => {
        if (typeof t === "string") fullText += (fullText ? "\n\n" : "") + t;
      };
      const addAttachmentText = async (att: Zotero.Item) => {
        try {
          if ((att as any).attachmentContentType === "application/pdf") {
            const t = await (att as any).attachmentText;
            pushText(t);
          }
        } catch (e) {
          // ignore
        }
      };

      if (item.isAttachment()) {
        await addAttachmentText(item);
      } else {
        const attachmentIDs = item.getAttachments();
        for (const id of attachmentIDs) {
          const att = Zotero.Items.get(id);
          await addAttachmentText(att);
        }
      }

      // Cap length to avoid oversize requests
      const MAX_CHARS = 30000; // ~safe cap before tokenization
      if (fullText.length > MAX_CHARS) {
        fullText = fullText.slice(0, MAX_CHARS) + "\n\n[...truncated...]";
      }

      if (!fullText) {
        return `Title: ${title}\nAbstract: ${abstractNote || "(none)"}`;
      }
      return `Title: ${title}\nAbstract: ${abstractNote || "(none)"}\n\nFull document excerpt (may be truncated):\n${fullText}`;
    } catch (e) {
      return this.buildContextText();
    }
  }

  private static buildQuickActionPrompt(actionKey: string, context: string): string {
    switch (actionKey) {
      case "summarize":
        return `Summarize the paper concisely for a graduate student.\n\n${context}`;
      case "research-method":
        return `Identify and describe the research method(s) used in the paper.\n\n${context}`;
      case "research-results":
        return `Extract the key results/findings of the paper with brief bullet points.\n\n${context}`;
      case "smart-note":
        return `Create a concise smart note that connects this paper to general research goals. Include 3-5 bullets and a one-sentence takeaway.\n\n${context}`;
      default:
        return `Help with the following action: ${actionKey}.\n\n${context}`;
    }
  }

  /**
   * EDIT 2025-08-08: Simple Markdown renderer for assistant messages - AA
   */
  private static renderMarkdown(doc: Document, md: string): HTMLElement {
    const container = doc.createElement("div");
    container.className = "md-container";

    const lines = md.replace(/\r\n?/g, "\n").split("\n");
    let inCode = false;
    let codeBuffer: string[] = [];
    let inUL = false;
    let inOL = false;
    let ulEl: HTMLElement | null = null;
    let olEl: HTMLElement | null = null;

    const flushLists = () => {
      if (inUL && ulEl) {
        container.appendChild(ulEl);
      }
      if (inOL && olEl) {
        container.appendChild(olEl);
      }
      inUL = false;
      inOL = false;
      ulEl = null;
      olEl = null;
    };

    const escapeHtml = (s: string) => s
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#39;");

    const renderInline = (s: string) => {
      let out = escapeHtml(s);
      // code (inline) first
      out = out.replace(/`([^`]+)`/g, (_m, g1) => `<code>${g1}</code>`);
      // links (http/https only)
      out = out.replace(/\[([^\]]+)\]\((https?:[^)\s]+)\)/g, (_m, text, url) => `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`);
      // bold then italic
      out = out.replace(/\*\*([^*]+)\*\*/g, (_m, g1) => `<strong>${g1}</strong>`);
      out = out.replace(/(^|[^*])\*([^*]+)\*/g, (_m, pre, g1) => `${pre}<em>${g1}</em>`);
      out = out.replace(/(^|[^_])_([^_]+)_/g, (_m, pre, g1) => `${pre}<em>${g1}</em>`);
      return out;
    };

    for (const rawLine of lines) {
      const line = rawLine;
      if (line.trim().startsWith("```") && !inCode) {
        // enter code
        flushLists();
        inCode = true;
        codeBuffer = [];
        continue;
      }
      if (line.trim().startsWith("```") && inCode) {
        // exit code
        const pre = doc.createElement("pre");
        const code = doc.createElement("code");
        code.innerText = codeBuffer.join("\n");
        pre.appendChild(code);
        container.appendChild(pre);
        inCode = false;
        codeBuffer = [];
        continue;
      }
      if (inCode) {
        codeBuffer.push(line);
        continue;
      }

      // lists
      const ulMatch = /^\s*[-*]\s+(.+)$/.exec(line);
      const olMatch = /^\s*\d+\.\s+(.+)$/.exec(line);
      if (ulMatch) {
        if (!inUL) {
          flushLists();
          inUL = true;
          ulEl = doc.createElement("ul");
        }
        const li = doc.createElement("li");
        li.innerHTML = renderInline(ulMatch[1]);
        ulEl!.appendChild(li);
        continue;
      } else if (olMatch) {
        if (!inOL) {
          flushLists();
          inOL = true;
          olEl = doc.createElement("ol");
        }
        const li = doc.createElement("li");
        li.innerHTML = renderInline(olMatch[1]);
        olEl!.appendChild(li);
        continue;
      } else if (line.trim() === "") {
        flushLists();
        // blank line => paragraph break
        const br = doc.createElement("br");
        container.appendChild(br);
        continue;
      } else {
        flushLists();
      }

      // headings
      const h3 = /^###\s+(.+)$/.exec(line);
      const h2 = /^##\s+(.+)$/.exec(line);
      const h1 = /^#\s+(.+)$/.exec(line);
      if (h3) {
        const el = doc.createElement("h3");
        el.innerHTML = renderInline(h3[1]);
        container.appendChild(el);
        continue;
      } else if (h2) {
        const el = doc.createElement("h4");
        el.innerHTML = renderInline(h2[1]);
        container.appendChild(el);
        continue;
      } else if (h1) {
        const el = doc.createElement("h4");
        el.innerHTML = renderInline(h1[1]);
        container.appendChild(el);
        continue;
      }

      const p = doc.createElement("div");
      p.className = "md-line";
      p.innerHTML = renderInline(line);
      container.appendChild(p);
    }

    flushLists();

    return container;
  }
}
