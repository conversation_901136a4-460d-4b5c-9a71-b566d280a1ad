import { getPref } from "../utils/prefs";

/**
 * EDIT 2025-08-08: Initial LLM backend with OpenRouter adapter - AA
 */

export type ChatRole = "system" | "user" | "assistant";

export interface ChatMessage {
  role: ChatRole;
  content: string;
}

export interface CallLLMOptions {
  messages: ChatMessage[];
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export async function callLLM(options: CallLLMOptions): Promise<string> {
  const provider = (getPref("llm.provider") as string) || "openrouter";
  const apiKey = (getPref("llm.apiKey") as string) || "";
  const modelPref = (getPref("llm.model") as string) || "deepseek/deepseek-r1-0528:free";
  const baseURLPref = (getPref("llm.baseURL") as string) || "";
  const timeoutMsPref = (getPref("llm.timeoutMs") as number) || 60000;
  const logVerbose = !!getPref("dev.logLLM");

  if (!apiKey) {
    throw new Error("Missing API key. Please set it in Preferences.");
  }

  // EDIT 2025-08-08: Replace AbortController (not defined in some Zotero scopes) with Promise.race timeout - AA
  const timeoutMs = Math.max(2000, timeoutMsPref);

  try {
    switch (provider) {
      case "openrouter": {
        const endpoint = (baseURLPref || "https://openrouter.ai/api/v1") + "/chat/completions";
        const body: any = {
          model: options.model || modelPref,
          messages: options.messages.map((m) => ({ role: m.role, content: m.content })),
          temperature: options.temperature ?? 0.7,
        };
        if (options.maxTokens) {
          body.max_tokens = options.maxTokens;
        }

        if (logVerbose) {
          ztoolkit.log("[LLM] OpenRouter request", { endpoint, body: redacted(body, apiKey) });
        }

        const fetchPromise = fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${apiKey}`,
            "HTTP-Referer": "https://github.com/hua3467/zotero-ai-plugin",
            "X-Title": "Zotero AI Assistant",
          },
          body: JSON.stringify(body),
        });
        const timeoutPromise = new Promise<Response>((_, reject) =>
          setTimeout(() => reject(new Error(`Request timed out after ${timeoutMs} ms`)), timeoutMs),
        );
        const resp = (await Promise.race([fetchPromise, timeoutPromise])) as Response;

        if (!resp.ok) {
          const text = await resp.text();
          throw new Error(`LLM error: ${resp.status} ${resp.statusText} - ${text}`);
        }

        const data = (await resp.json()) as any;
        const content: string | undefined = data?.choices?.[0]?.message?.content;
        if (logVerbose) {
          ztoolkit.log("[LLM] OpenRouter response", truncateForLog(data));
        }
        if (!content) {
          throw new Error("Empty response from model");
        }
        return content;
      }
      default:
        throw new Error(`Provider not implemented: ${provider}`);
    }
  } finally {
    // no-op
  }
}

function redacted(obj: any, apiKey: string) {
  try {
    const clone = JSON.parse(JSON.stringify(obj));
    return clone;
  } catch {
    return obj;
  }
}

function truncateForLog(data: any) {
  try {
    const clone = JSON.parse(JSON.stringify(data));
    const text = JSON.stringify(clone);
    if (text.length > 5000) {
      return text.slice(0, 5000) + "…";
    }
    return clone;
  } catch {
    return data;
  }
}
