import {
  BasicExampleFactory,
  HelperExampleFactory,
  KeyExampleFactory,
  PromptExampleFactory,
  UIExampleFactory,
} from "./modules/examples";
import { PaperAssistantFactory } from "./modules/paperAssistant";
import { getString, initLocale } from "./utils/locale";
import { registerPrefsScripts } from "./modules/preferenceScript";
import { createZToolkit } from "./utils/ztoolkit";

async function onStartup() {
  await Promise.all([
    Zotero.initializationPromise,
    Zotero.unlockPromise,
    Zotero.uiReadyPromise,
  ]);

  initLocale();

  // Register Paper Assistant functionality only once
  PaperAssistantFactory.registerToolbarButton();

  BasicExampleFactory.registerPrefs();

  BasicExampleFactory.registerNotifier();

  KeyExampleFactory.registerShortcuts();

  await UIExampleFactory.registerExtraColumn();

  await UIExampleFactory.registerExtraColumnWithCustomCell();

  UIExampleFactory.registerItemPaneCustomInfoRow();

  UIExampleFactory.registerItemPaneSection();

  UIExampleFactory.registerReaderItemPaneSection();

  await Promise.all(
    Zotero.getMainWindows().map((win) => onMainWindowLoad(win)),
  );

  // Mark initialized as true to confirm plugin loading status
  // outside of the plugin (e.g. scaffold testing process)
  addon.data.initialized = true;
}

async function onMainWindowLoad(win: _ZoteroTypes.MainWindow): Promise<void> {
  // Create ztoolkit for every window
  addon.data.ztoolkit = createZToolkit();

  win.MozXULElement.insertFTLIfNeeded(
    `${addon.data.config.addonRef}-mainWindow.ftl`,
  );

  const popupWin = new ztoolkit.ProgressWindow(addon.data.config.addonName, {
    closeOnClick: true,
    closeTime: -1,
  })
    .createLine({
      text: getString("startup-begin"),
      type: "default",
      progress: 0,
    })
    .show();

  await Zotero.Promise.delay(1000);
  popupWin.changeLine({
    progress: 30,
    text: `[30%] ${getString("startup-begin")}`,
  });

  UIExampleFactory.registerStyleSheet(win);

  // Register Paper Assistant stylesheet
  PaperAssistantFactory.registerStyleSheet(win);

  // Ensure menu entries are present for this window (in case early registration missed it)
  PaperAssistantFactory.registerToolbarButton();

  // Commented out example menu items that were causing duplicate "AI Assistant" entries
  // UIExampleFactory.registerRightClickMenuItem();
  // UIExampleFactory.registerRightClickMenuPopup(win);
  // UIExampleFactory.registerWindowMenuWithSeparator();

  PromptExampleFactory.registerNormalCommandExample();

  PromptExampleFactory.registerAnonymousCommandExample(win);

  PromptExampleFactory.registerConditionalCommandExample();

  // Listen for reader tab selection changes to refresh assistant context
  try {
    ztoolkit.log("Setting up tab selection listeners");

    // Primary listener: TabSelect event on tabbar
    const tabContainer = (win.document.getElementById("tabbar") || win.document) as any;
    if (tabContainer) {
      tabContainer.addEventListener("TabSelect", (ev: any) => {
        try {
          ztoolkit.log("TabSelect event fired:", ev);
          const tab = ev?.target || ev?.detail?.tab || ev?.currentTarget?.selectedItem;
          const tabId = tab?.id || tab?.getAttribute?.("id");
          ztoolkit.log(`TabSelect: extracted tab ID: ${tabId}`);

          if (tabId) {
            (PaperAssistantFactory as any).setActiveReaderByTabId?.(String(tabId));
          } else {
            ztoolkit.log("TabSelect: No tab ID found, calling contextChanged");
            (PaperAssistantFactory as any).contextChanged?.();
          }
        } catch (e) {
          ztoolkit.log("Error in TabSelect handler:", e);
        }
      }, true);
      ztoolkit.log("TabSelect listener added to tabbar");
    } else {
      ztoolkit.log("Warning: Could not find tabbar element");
    }

    // Secondary listener: Zotero_Tabs API
    try {
      const zTabs = (ztoolkit.getGlobal("Zotero_Tabs") as any);
      if (zTabs && typeof zTabs.on === "function") {
        zTabs.on("select", (id: string) => {
          try {
            ztoolkit.log(`Zotero_Tabs select event: ${id}`);
            (PaperAssistantFactory as any).setActiveReaderByTabId?.(String(id));
          } catch (e) {
            ztoolkit.log("Error in Zotero_Tabs select handler:", e);
          }
        });
        ztoolkit.log("Zotero_Tabs select listener added");
      } else {
        ztoolkit.log("Zotero_Tabs API not available or doesn't support 'on' method");
      }
    } catch (e) {
      ztoolkit.log("Error setting up Zotero_Tabs listener:", e);
    }

    // Additional listener: Direct tab click events
    try {
      const tabs = win.document.querySelectorAll('tab[id]');
      tabs.forEach((tab: any) => {
        tab.addEventListener('click', () => {
          const tabId = tab.id || tab.getAttribute('id');
          if (tabId) {
            ztoolkit.log(`Direct tab click: ${tabId}`);
            // Small delay to ensure tab selection is processed
            setTimeout(() => {
              (PaperAssistantFactory as any).setActiveReaderByTabId?.(String(tabId));
            }, 100);
          }
        });
      });
      ztoolkit.log(`Added click listeners to ${tabs.length} tabs`);
    } catch (e) {
      ztoolkit.log("Error setting up direct tab click listeners:", e);
    }

  } catch (e) {
    ztoolkit.log("Error setting up tab listeners:", e);
  }

  await Zotero.Promise.delay(1000);

  popupWin.changeLine({
    progress: 100,
    text: `[100%] ${getString("startup-finish")}`,
  });
  popupWin.startCloseTimer(5000);

  // Commented out example dialog call
  // addon.hooks.onDialogEvents("dialogExample");
}

async function onMainWindowUnload(win: Window): Promise<void> {
  ztoolkit.unregisterAll();
  addon.data.dialog?.window?.close();
}

function onShutdown(): void {
  ztoolkit.unregisterAll();
  PaperAssistantFactory.reset();
  addon.data.dialog?.window?.close();
  // Remove addon object
  addon.data.alive = false;
  // @ts-expect-error - Plugin instance is not typed
  delete Zotero[addon.data.config.addonInstance];
}

/**
 * This function is just an example of dispatcher for Notify events.
 * Any operations should be placed in a function to keep this funcion clear.
 */
async function onNotify(
  event: string,
  type: string,
  ids: Array<string | number>,
  extraData: { [key: string]: any },
) {
  // You can add your code to the corresponding notify type
  ztoolkit.log("notify", event, type, ids, extraData);
  if (
    event == "select" &&
    type == "tab" &&
    extraData[ids[0]].type == "reader"
  ) {
    BasicExampleFactory.exampleNotifierCallback();
  } else {
    return;
  }
}

/**
 * This function is just an example of dispatcher for Preference UI events.
 * Any operations should be placed in a function to keep this funcion clear.
 * @param type event type
 * @param data event data
 */
async function onPrefsEvent(type: string, data: { [key: string]: any }) {
  switch (type) {
    case "load":
      registerPrefsScripts(data.window);
      break;
    default:
      return;
  }
}

function onShortcuts(type: string) {
  switch (type) {
    case "larger":
      KeyExampleFactory.exampleShortcutLargerCallback();
      break;
    case "smaller":
      KeyExampleFactory.exampleShortcutSmallerCallback();
      break;
    default:
      break;
  }
}

function onDialogEvents(type: string) {
  switch (type) {
    case "dialogExample":
      HelperExampleFactory.dialogExample();
      break;
    case "clipboardExample":
      HelperExampleFactory.clipboardExample();
      break;
    case "filePickerExample":
      HelperExampleFactory.filePickerExample();
      break;
    case "progressWindowExample":
      HelperExampleFactory.progressWindowExample();
      break;
    case "vtableExample":
      HelperExampleFactory.vtableExample();
      break;
    case "showPaperAssistant":
      PaperAssistantFactory.showPaperAssistant();
      break;
    default:
      break;
  }
}

// Add your hooks here. For element click, etc.
// Keep in mind hooks only do dispatch. Don't add code that does real jobs in hooks.
// Otherwise the code would be hard to read and maintain.

export default {
  onStartup,
  onShutdown,
  onMainWindowLoad,
  onMainWindowUnload,
  onNotify,
  onPrefsEvent,
  onShortcuts,
  onDialogEvents,
};
