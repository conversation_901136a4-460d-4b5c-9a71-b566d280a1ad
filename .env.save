# Usage: 
# Copy this file as `.env` and fill in the variables below as instructed.

# If you are developing more than one plugin, you can store the bin path and 
# profile path in the system environment variables, which can be omitted here.

# The path of the Zotero binary file.
# The path delimiter should be escaped as `\\` for win32. 
# The path is `*/Zotero.app/Contents/MacOS/zotero` for MacOS.

ZOTERO_PLUGIN_ZOTERO_BIN_PATH=/Applications/Zotero.app/Contents/MacOS/zotero
ZOTERO_PLUGIN_PROFILE_PATH=

# The path of the profile used for development.
# Start the profile manager by `/path/to/zotero.exe -p` to create a profile for development.
# @see https://www.zotero.org/support/kb/profile_directory
ZOTERO_PLUGIN_PROFILE_PATH = /path/to/profile

# The directory where the database is located.
# If this field is kept empty, <PERSON>oter<PERSON> will start with the default data.
# @see https://www.zotero.org/support/zotero_data
ZOTERO_PLUGIN_DATA_DIR =

# Custom commands to kill Zotero processes.
# Commands for different platforms are already built into zotero-plugin, 
# if the built-in commands are not suitable for your needs, please modify this variable.
# ZOTERO_PLUGIN_AKILL_COMMAND = 

# GitHub Token
# For scaffold auto create release and upload assets.
# Fill in this variable if you are publishing locally instead of CI.
# GITHUB_TOKEN =
