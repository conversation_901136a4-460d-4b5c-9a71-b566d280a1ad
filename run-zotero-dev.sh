#!/bin/bash

# <PERSON>ript to run Zotero in developer mode with the AI Assistant plugin

echo "Starting Zotero in developer mode..."

# Check if Zotero is installed
ZOTERO_PATH=""

# Common Zotero installation paths on macOS
if [ -d "/Applications/Zotero.app" ]; then
    ZOTERO_PATH="/Applications/Zotero.app/Contents/MacOS/zotero"
elif [ -d "/Applications/Zotero Beta.app" ]; then
    ZOTERO_PATH="/Applications/Zotero Beta.app/Contents/MacOS/zotero"
fi

if [ -z "$ZOTERO_PATH" ]; then
    echo "Error: Zotero not found in /Applications/"
    echo "Please install Zotero from https://www.zotero.org/download/"
    exit 1
fi

echo "Found Zotero at: $ZOTERO_PATH"

# Build the plugin first
echo "Building plugin..."
npm run build

if [ $? -ne 0 ]; then
    echo "Error: Plugin build failed"
    exit 1
fi

echo "Plugin built successfully"

# Create a temporary profile directory for development
DEV_PROFILE_DIR="$HOME/.zotero-dev-profile"
mkdir -p "$DEV_PROFILE_DIR"

echo "Using development profile at: $DEV_PROFILE_DIR"

# Start Zotero with developer options
echo "Starting Zotero with developer console..."
echo "The developer console will open automatically."
echo "You can also open it manually with Ctrl+Shift+I (Cmd+Shift+I on Mac)"
echo ""

# Run Zotero with development flags
"$ZOTERO_PATH" \
    -profile "$DEV_PROFILE_DIR" \
    -purgecaches \
    -ZoteroDebugText \
    -jsconsole &

# Wait a moment for Zotero to start
sleep 3

echo ""
echo "Zotero should now be running in developer mode."
echo ""
echo "To install the plugin:"
echo "1. In Zotero, go to Tools > Add-ons"
echo "2. Click the gear icon and select 'Install Add-on From File...'"
echo "3. Navigate to: $(pwd)/.scaffold/build/zotero-ai-assistant.xpi"
echo "4. Select the file and click 'Open'"
echo "5. Restart Zotero when prompted"
echo ""
echo "Plugin file location: $(pwd)/.scaffold/build/zotero-ai-assistant.xpi"
