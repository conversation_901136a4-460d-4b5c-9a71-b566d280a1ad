# Zotero AI Plugin Codebase Overview

This document provides an overview of the Zotero AI Plugin codebase, its structure, and key components.

## 1. Project Overview

The Zotero AI Plugin is an add-on for Zotero 7 that provides an AI-powered "Paper Assistant". This assistant is designed to help users with tasks like summarizing papers, identifying research methods, and taking smart notes.

The assistant's UI is implemented as a panel that can be opened either within the Zotero PDF reader or in the main Zotero window. It features a chat interface and a set of "quick action" buttons.

As of the current version, the UI is in place, but the AI functionalities are placeholders and not yet implemented.

## 2. Project Structure

The project is organized into several key directories:

-   `addon/`: Contains the files that will be packaged into the Zotero plugin (`.xpi` file).
    -   `bootstrap.js`: The main entry point for the Zotero plugin.
    -   `content/`: UI-related files like CSS, icons, and generated JavaScript.
    -   `locale/`: Fluent localization files (`.ftl`).
    -   `manifest.json`: The plugin manifest file (template).
-   `src/`: The TypeScript source code for the plugin.
    -   `index.ts`: The TypeScript entry point.
    -   `addon.ts`: Defines the main `Addon` class.
    -   `hooks.ts`: Manages Zotero lifecycle events (startup, shutdown, window loading).
    -   `modules/`: Contains the core features of the plugin.
        -   `paperAssistant.ts`: Implements the "Paper Assistant" UI and logic.
        -   `examples.ts`: Example code demonstrating various `zotero-plugin-toolkit` features.
    -   `utils/`: Utility functions for locale, preferences, etc.
-   `doc/`: Documentation files.
-   `package.json`: Defines project metadata, dependencies, and build scripts.
-   `zotero-plugin.config.ts`: Configuration for the `zotero-plugin-scaffold`.

## 3. Core Components & Logic Flow

### 3.1. Plugin Initialization

1.  **`addon/bootstrap.js`**: When Zotero starts, it loads this script. This is the first file to be executed.
2.  The `startup` function in `bootstrap.js` is called.
3.  It loads the compiled JavaScript from the `src` directory (`/content/scripts/zotero-ai-assistant.js`) into the plugin's sandbox environment.
4.  It calls the `onStartup` hook on the global addon instance.

### 3.2. TypeScript Entry Point

1.  **`src/index.ts`**: This is the main entry point for the TypeScript code.
2.  It creates a new instance of the `Addon` class from `src/addon.ts`.
3.  It attaches this `Addon` instance to the global `Zotero` object, making it accessible throughout the plugin (e.g., as `Zotero.ZoteroAIAssistant`).

### 3.3. The `Addon` Class

-   **`src/addon.ts`**: This file defines the `Addon` class, which acts as the central container for the plugin's data, hooks, and APIs.
-   In its constructor, it initializes:
    -   `data`: An object to hold state, configuration, and instances of utilities like `ztoolkit`.
    -   `hooks`: An object containing the lifecycle functions defined in `src/hooks.ts`.
    -   `api`: An empty object for future API exposure.

### 3.4. Lifecycle Hooks

-   **`src/hooks.ts`**: This file is the control center for the plugin's behavior. It exports an object with functions that are called at different points in the Zotero application lifecycle.
-   `onStartup()`: Called when the plugin is first loaded. It initializes the locale and registers menu items.
-   `onMainWindowLoad()`: Called every time a main Zotero window is opened. It is responsible for injecting stylesheets, creating UI elements, and registering window-specific components.
-   `onShutdown()`: Called when the plugin is disabled or Zotero is closed. It performs cleanup tasks.
-   `onDialogEvents()`: A dispatcher for custom events, used to trigger actions like showing the Paper Assistant panel.

### 3.5. Paper Assistant Module

-   **`src/modules/paperAssistant.ts`**: This is where the core functionality of the plugin resides.
-   `PaperAssistantFactory` is a class with static methods to:
    -   Register a toolbar button in the main Zotero window.
    -   Show the assistant as a floating, movable window (`showPaperAssistant`).
    -   Create the assistant's UI dynamically (`createFloatingWindowContent`), including the header, quick actions, and chat sections.
    -   Handle user interactions (button clicks, chat messages), although the AI logic is currently a placeholder.
    -   Provide context-aware welcome messages based on whether a PDF is open or items are selected.

## 4. Build Process

The project uses `zotero-plugin-scaffold`, a tool that simplifies the development and building of Zotero plugins.

-   `npm run build`: This command, defined in `package.json`, runs `zotero-plugin build`. This process:
    1.  Compiles the TypeScript code from `src/` into JavaScript.
    2.  Copies the compiled JS and other assets (CSS, icons, locales) into the `addon/` directory.
    3.  Replaces placeholders (like `__addonName__`, `__addonRef__`) in `manifest.json` and `bootstrap.js` with values from `package.json`.
    4.  Packages the `addon/` directory into a distributable `.xpi` file.

## 5. Recent Changes (UI Implementation)

### 5.1. Toolbar Integration
The AI Assistant is now accessible via a toolbar button instead of menu items:
- **Toolbar Button**: Added `#ai-assistant-toolbar-button` to the main Zotero toolbar
- **Icon Integration**: Uses the plugin's favicon as the button icon
- **Dynamic Registration**: Button is added to each window as it loads

### 5.2. Floating Window Architecture
The assistant now opens as a standalone, movable window:
- **Window Management**: Uses `openDialog()` to create floating windows
- **Custom Content**: Replaces default dialog content with AI Assistant UI
- **Context Awareness**: Welcome messages adapt based on current state (open PDF, selected items, etc.)
- **Window Lifecycle**: Proper cleanup when plugin is disabled or windows are closed

### 5.3. Enhanced User Experience
- **Single Instance**: Only one floating window can be open at a time
- **Focus Management**: Clicking the toolbar button focuses existing window if already open
- **Responsive Layout**: CSS updated to work in both floating window and legacy panel modes
- **Visual Polish**: Added hover effects and improved styling

## 6. Adding New Features

To add a new feature, you would typically:

1.  Create a new module in the `src/modules/` directory (e.g., `src/modules/newFeature.ts`).
2.  Implement the feature's logic within a class or a set of functions in this new file.
3.  In `src/hooks.ts`, import your new module.
4.  Call your module's registration/initialization functions from the appropriate lifecycle hooks (e.g., register a menu item in `onStartup`, create UI in `onMainWindowLoad`).
5.  If the feature requires user interaction, you can dispatch an event to `onDialogEvents` to trigger its functionality.
