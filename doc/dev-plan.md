# Development Plan: Zotero AI Plugin

Last updated: 2025-08-08

This plan tracks milestones, current status, and concrete next actions so future development is unambiguous.

## Status Dashboard

- **Milestone 1 — Core Setup & Paper Assistant UI**: Completed
- **Milestone 2 — API Integration & Basic AI Actions**: Core complete (chat + quick actions live via LLM); enhancements pending; added optional “Send Full Document” toggle with truncated full‑text snippet context
- **Milestone 3 — Research Plan & Smart Notes**: Not started
- **Milestone 4 — Literature Filter & Final Polish**: Not started

## Milestone 1: Core Setup & Paper Assistant UI (Completed)

Objective: Establish the plugin foundation and create the primary user interface for interacting with documents.

What’s done:
- Plugin boilerplate and build system in place
- PaperAssistantFactory module (`src/modules/paperAssistant.ts`)
- Access via Tools menu and item context menu
- Floating chat UI with 4 quick action buttons
- Styles in `addon/content/paperAssistant.css`
- Localization scaffolding for en-US and zh-CN
- Registered in lifecycle hooks (`src/hooks.ts`)

Definition of Done: Users can open a floating AI Assistant window, type in the chat, and see responses/quick action outputs. Achieved.

## Milestone 2: API Integration & Basic AI Actions

Objective: Connect to LLM providers and implement initial AI features for chat and quick actions.

Status: **Core completed**

Delivered:
1) API Management UI (Preferences)
- Provider, API key, Model, Base URL (optional), Timeout, Verbose logging (dev) in `addon/content/preferences.xhtml`
- Defaults in `addon/prefs.js`; typed keys in `typings/prefs.d.ts`; i18n in `addon/locale/*/preferences.ftl`

2) Backend LLM Service
- Implemented `src/modules/llm.ts` (OpenRouter adapter) with timeout via Promise.race (no AbortController requirement)
- Redacted logging, basic error messages surfaced to UI

3) Quick Actions
- `handleQuickAction()` builds prompts and calls LLM
- Context v1: item title + abstract; selection/fulltext not yet included
- Busy/"Thinking…" state shown

4) Conversational Chat
- Sends message with lightweight context prefix; renders assistant replies
- Assistant replies now rendered with lightweight Markdown (headings, lists, code, links)
- Scrollable message area; input stays pinned at bottom

Next enhancements (post-core):
- Reader selection capture and inclusion in context (v2)
- RAG pipeline for “Send Full Document”: local embeddings + vector search (sqlite-vec) to select chunks; optional page images; configurable providers
- Custom quick action prompts editable in preferences
- Retry with backoff for 429/5xx; friendlier error taxonomy
- Streaming responses (UI incremental render)
- Additional provider adapters (OpenAI/Claude/Gemini/DeepSeek native)
- Persist short conversation history in-window (optional cross-window persistence later)

Acceptance criteria:
- With a valid API key, pressing “Summarize” produces a model response in chat — Achieved
- Free-form chat sends and displays model responses — Achieved
- Errors surface as assistant messages without breaking the window — Achieved

## Milestone 3: Research Plan & Smart Notes

Objective: Introduce "Research Plan" to personalize outputs and enable Smart Notes.

Status: Not started

Design decisions:
- Storage: research plans as Zotero Notes; manage in collection `AI Research Plans` with tag `ai-research-plan`
- Active plan tracking: store active plan note key in prefs (e.g., `plans.activeKey`)

Planned deliverables:
1) Research Plan Management UI
- Tools → “Research Plans…” dialog (list, create/edit/delete, set active)
- Implementation: `src/modules/researchPlans.ts` + `ztoolkit.Dialog`

2) Smart Note Action
- Reads active plan note + current item context; calls LLM; creates a child note

3) Custom Quick Actions
- Preferences table for custom actions (name + prompt template with variables like {title}, {abstract}, {plan})

Next actions (step-by-step):
- Implement `researchPlans.ts` for collection bootstrap and dialog CRUD
- Add prefs for `plans.activeKey` and helpers
- Render custom quick actions from prefs
- Implement Smart Note write-back path

Acceptance criteria:
- Users can create and set an active plan
- “Smart Note” creates a child note for the current item using plan context
- Custom quick actions from prefs appear and work

## Milestone 4: Literature Filter & Final Polish

Objective: Filter collection items for relevance to the active research plan and tag them.

Status: Not started

Planned deliverables:
1) Filter UI
- Tools → “Filter by Research Plan” and/or plugin pane button
- Preferences: tag name to apply (default: `AI-Relevant`) and threshold policy

2) Filtering Logic
- Use current collection via `Zotero.getActiveZoteroPane().getSelectedCollection()`
- For each regular item: use `abstractNote` + plan context to ask LLM for relevance (Yes/No + rationale)
- If relevant, add configured tag; show progress and allow cancel; rate-limit requests

3) Finalization
- Functional tests for: chat, quick actions, smart note, filter tagging
- UI polish and localization pass
- User docs and packaging

Next actions (step-by-step):
- Build filtering runner with progress window and cancellation
- Implement tag write-back and transaction batching where appropriate
- Add preferences for tag name and thresholds

Acceptance criteria:
- Running the filter over a collection tags relevant items as configured
- Operation is cancellable and shows progress/errors

## Technical Notes & References

- Zotero 7 APIs used: `Zotero.PreferencePanes`, `Zotero.ItemPaneManager`, `Zotero.Reader`, `Zotero.Search`, `Zotero.Items`, `Zotero.Prefs`, `Zotero.getActiveZoteroPane()`
- Context privacy (current): by default only metadata — title + abstract — is sent; when “Send Full Document” is enabled, we include a truncated full‑text snippet (up to ~8k chars) from the active PDF; full uploads are not performed
- Planned context evolution: (v2) reader selection; (v3) short full-text snippets (opt-in)
- Provider default: OpenRouter with `deepseek/deepseek-r1-0528:free`; users can change provider/model in preferences
- Error handling: timeout via Promise.race; future: retry/backoff and granular messages

---

## Milestone 1 Summary (Completed)

- Boilerplate and build configured
- Paper Assistant UI with quick actions and chat
- Menu integration: Tools and item context menu
- Stylesheet loading and localization set up
- Hook registrations and lifecycle management working
