# How to Install the AI Assistant Plugin

## Plugin File Location
The plugin file is located at: `zotero-ai-assistant.xpi` in this directory.

## Installation Steps

### Method 1: Install from File (Recommended)
1. **Open Zotero** (make sure it's running)
2. Go to **Tools > Add-ons**
3. Click the **gear icon** (⚙️) in the top-right corner
4. Select **"Install Add-on From File..."**
5. Navigate to this directory: `/Users/<USER>/Documents/Research/zotero-ai-plugin/`
6. Select the file: **`zotero-ai-assistant.xpi`**
7. Click **"Open"**
8. **Restart Zotero** when prompted

### Method 2: Drag and Drop
1. **Open Zotero**
2. Go to **Tools > Add-ons**
3. **Drag and drop** the `zotero-ai-assistant.xpi` file into the Add-ons window
4. **Restart Zotero** when prompted

## After Installation

### Check if Plugin is Working
1. **Open Browser Console** (Cmd+Shift+J on Mac, Ctrl+Shift+J on Windows/Linux)
2. Look for messages starting with "AI Assistant plugin"
3. You should see:
   - "AI Assistant plugin onStartup() called"
   - "AI Assistant plugin: Zotero initialization complete"
   - "AI Assistant plugin: Menu registration complete"

### Look for Menu Items
After restarting Zotero, you should see:
1. **Right-click context menu**: Right-click on any item in your library → "AI Assistant"
2. **Tools menu**: Tools → "AI Assistant"

## Troubleshooting

### If Menu Items Don't Appear
1. Check the Browser Console for error messages
2. Make sure Zotero was restarted after installation
3. Try uninstalling and reinstalling the plugin

### If Installation Fails
1. Make sure Zotero is version 7.0 or higher
2. Try closing Zotero completely and reopening it
3. Check that the XPI file is not corrupted (should be about 46KB)

### Debug Mode
If you need to debug issues:
1. Run Zotero with: `./run-zotero-dev.sh`
2. This will open Zotero with developer console automatically
3. Install the plugin and check console messages

## Plugin Features (Milestone 1)
- Paper Assistant UI with chat interface
- Quick action buttons (Summarize, Research Method, Research Results, Smart Note)
- Integration with Zotero's menu system
- Basic UI framework ready for AI integration in Milestone 2
